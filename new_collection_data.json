{"validation_type_key": "tag_titles", "properties": [{"validation_rules": [{"id": "title_presence_validation", "validation_type": "EXPRESSION_TYPE_LIST", "expressions": ["bill_of_sale.title_received.value == True or bill_of_sale.title_received.value == \"true\" or bill_of_sale.title_received.value == 1"], "error_msgs": ["Title Not Yet Received: Deal assigned for processing before titlemso or MSO is physically or digitally available"], "conditionType": "AND", "groups": []}]}, {"validation_rules": [{"id": "reassignment_mv7d_validation", "validation_type": "EXPRESSION_TYPE_LIST", "expressions": ["not (bill_of_sale.vehicle_type.value == \"used\" and bill_of_sale.title_fully_assigned.value == True) or (red_reassignment.vin.value != None and red_reassignment.vin.value != \"\")"], "error_msgs": ["Reassignment Needed but Not Included: Used vehicle with titlemso fully reassigned, but no MV-7D included"], "conditionType": "AND", "groups": []}]}, {"validation_rules": [{"id": "title_chain_sequence_validation", "validation_type": "EXPRESSION_TYPE_LIST", "expressions": ["(titlemso.selling_dealer_name.value != None and titlemso.selling_dealer_name.value != \"\" and titlemso.selling_dealer_name.value != \"N/A\") and (titlemso.buyer_name.value == red_reassignment.buyer_name.value) and (red_reassignment.buyer_name.value == bill_of_sale.buyer_name.value) and (bill_of_sale.buyer_name.value == mv-1.buyer_full_name.value)"], "error_msgs": ["Title Chain Broken or Not Assigned: Missing reassignment between owner and dealership - buyer names must flow correctly through titlemso â†’ reassignment â†’ sale â†’ registration"], "conditionType": "AND", "groups": []}]}, {"validation_rules": [{"id": "incomplete_reassignment_validation", "validation_type": "EXPRESSION_TYPE_LIST", "expressions": ["(red_reassignment.vin.value != None and red_reassignment.vin.value != \"\") and (red_reassignment.odometer_reading.value != None and red_reassignment.odometer_reading.value != \"\") and (red_reassignment.date_of_reassignment.value != None and red_reassignment.date_of_reassignment.value != \"\") and (red_reassignment.buyer_name.value != None and red_reassignment.buyer_name.value != \"\")"], "error_msgs": ["Incomplete Reassignment: Reassignment form missing required signatures, VIN, mileage, or transfer date"], "conditionType": "AND", "groups": []}]}, {"validation_rules": [{"id": "bill_of_sale.deal_number.value", "validation_type": "REGEX_LIST", "regexes": ["^.{1,}$", "^[A-Za-z0-9]+$"], "error_msgs": ["Deal number is required", "Deal number should be alphanumeric"], "conditionType": "AND", "groups": ["bill_of_sale"]}]}, {"validation_rules": [{"id": "mv-1.deal_number.value", "validation_type": "REGEX_LIST", "regexes": ["^.{1,}$", "^[A-Za-z0-9]+$"], "error_msgs": ["Deal number is required", "Deal number should be alphanumeric"], "conditionType": "AND", "groups": ["mv-1"]}]}, {"validation_rules": [{"id": "bill_of_sale.stock_number.value", "validation_type": "REGEX_LIST", "regexes": ["^[A-Za-z0-9-]{1,20}$"], "error_msgs": ["Stock number is required and should be alphanumeric with hyphens (1-20 characters)"], "conditionType": "AND", "groups": ["bill_of_sale"]}]}, {"validation_rules": [{"id": "mv-1.stock_number.value", "validation_type": "REGEX_LIST", "regexes": ["^[A-Za-z0-9-]{1,20}$"], "error_msgs": ["Stock number is required and should be alphanumeric with hyphens (1-20 characters)"], "conditionType": "AND", "groups": ["mv-1"]}]}, {"validation_rules": [{"id": "bill_of_sale.vin.value", "validation_type": "REGEX_LIST", "regexes": ["^.{1,}$", "^[A-HJ-NPR-Z0-9]{17}$"], "error_msgs": ["VIN is required", "VIN should be exactly 17 characters (excluding I, O, Q)"], "conditionType": "AND", "groups": ["bill_of_sale"]}, {"id": "bill_of_sale.vin.consistency", "validation_type": "EXPRESSION_TYPE_LIST", "expressions": ["bill_of_sale.vin.value == mv-1.vin.value and mv-1.vin.value == titlemso.vin.value and titlemso.vin.value == red_reassignment.vin.value or bill_of_sale.vin.value == mv7d.vin.value and mv-1.vin.value == titlemso.vin.value and titlemso.vin.value == mv7d.vin.value"], "error_msgs": ["VIN number does not match across documents"], "conditionType": "AND", "groups": ["bill_of_sale"]}]}, {"validation_rules": [{"id": "mv-1.vin.value", "validation_type": "REGEX_LIST", "regexes": ["^.{1,}$", "^[A-HJ-NPR-Z0-9]{17}$"], "error_msgs": ["VIN is required", "VIN should be exactly 17 characters (excluding I, O, Q)"], "conditionType": "AND", "groups": ["mv-1"]}, {"id": "mv-1.vin.consistency", "validation_type": "EXPRESSION_TYPE_LIST", "expressions": ["bill_of_sale.vin.value == mv-1.vin.value and mv-1.vin.value == titlemso.vin.value and titlemso.vin.value == red_reassignment.vin.value or bill_of_sale.vin.value == mv7d.vin.value and mv-1.vin.value == titlemso.vin.value and titlemso.vin.value == mv7d.vin.value"], "error_msgs": ["VIN number does not match across documents"], "conditionType": "AND", "groups": ["mv-1"]}]}, {"validation_rules": [{"id": "titlemso.vin.value", "validation_type": "REGEX_LIST", "regexes": ["^.{1,}$", "^[A-HJ-NPR-Z0-9]{17}$"], "error_msgs": ["VIN is required", "VIN should be exactly 17 characters (excluding I, O, Q)"], "conditionType": "AND", "groups": ["<PERSON><PERSON><PERSON>"]}, {"id": "titlemso.vin.consistency", "validation_type": "EXPRESSION_TYPE_LIST", "expressions": ["bill_of_sale.vin.value == mv-1.vin.value and mv-1.vin.value == titlemso.vin.value and titlemso.vin.value == red_reassignment.vin.value or bill_of_sale.vin.value == mv7d.vin.value and mv-1.vin.value == titlemso.vin.value and titlemso.vin.value == mv7d.vin.value"], "error_msgs": ["VIN number does not match across documents"], "conditionType": "AND", "groups": ["<PERSON><PERSON><PERSON>"]}]}, {"validation_rules": [{"id": "red_reassignment.vin.value", "validation_type": "REGEX_LIST", "regexes": ["^.{1,}$", "^[A-HJ-NPR-Z0-9]{17}$"], "error_msgs": ["VIN is required", "VIN should be exactly 17 characters (excluding I, O, Q)"], "conditionType": "AND", "groups": ["red_reassignment"]}, {"id": "red_reassignment.vin.consistency", "validation_type": "EXPRESSION_TYPE_LIST", "expressions": ["bill_of_sale.vin.value == mv-1.vin.value and mv-1.vin.value == titlemso.vin.value and titlemso.vin.value == red_reassignment.vin.value or bill_of_sale.vin.value == mv7d.vin.value and mv-1.vin.value == titlemso.vin.value and titlemso.vin.value == mv7d.vin.value"], "error_msgs": ["VIN number does not match across documents"], "conditionType": "AND", "groups": ["red_reassignment"]}]}, {"validation_rules": [{"id": "mv7d.vin.value", "validation_type": "REGEX_LIST", "regexes": ["^.{1,}$", "^[A-HJ-NPR-Z0-9]{17}$"], "error_msgs": ["VIN is required", "VIN should be exactly 17 characters (excluding I, O, Q)"], "conditionType": "AND", "groups": ["mv7d"]}, {"id": "mv7d.vin.consistency", "validation_type": "EXPRESSION_TYPE_LIST", "expressions": ["bill_of_sale.vin.value == mv-1.vin.value and mv-1.vin.value == titlemso.vin.value and titlemso.vin.value == red_reassignment.vin.value or bill_of_sale.vin.value == mv7d.vin.value and mv-1.vin.value == titlemso.vin.value and titlemso.vin.value == mv7d.vin.value"], "error_msgs": ["VIN number does not match across documents"], "conditionType": "AND", "groups": ["mv7d"]}]}, {"validation_rules": [{"id": "bill_of_sale.year.value", "validation_type": "REGEX_LIST", "regexes": ["^(19[0-9]{2}|20[0-2][0-9]|2030)$"], "error_msgs": ["Year is required and should be between 1900 and 2030"], "conditionType": "AND", "groups": ["bill_of_sale"]}, {"id": "bill_of_sale.year.consistency", "validation_type": "EXPRESSION_TYPE_LIST", "expressions": ["bill_of_sale.year.value == mv-1.year.value and mv-1.year.value == titlemso.year.value and titlemso.year.value == red_reassignment.year.value or bill_of_sale.year.value == mv-1.year.value and mv-1.year.value == titlemso.year.value and titlemso.year.value == mv7d.year.value"], "error_msgs": ["Year does not match across documents"], "conditionType": "AND", "groups": ["bill_of_sale"]}]}, {"validation_rules": [{"id": "mv-1.year.value", "validation_type": "REGEX_LIST", "regexes": ["^(19[0-9]{2}|20[0-2][0-9]|2030)$"], "error_msgs": ["Year is required and should be between 1900 and 2030"], "conditionType": "AND", "groups": ["mv-1"]}, {"id": "mv-1.year.consistency", "validation_type": "EXPRESSION_TYPE_LIST", "expressions": ["bill_of_sale.year.value == mv-1.year.value and mv-1.year.value == titlemso.year.value and titlemso.year.value == red_reassignment.year.value or bill_of_sale.year.value == mv-1.year.value and mv-1.year.value == titlemso.year.value and titlemso.year.value == mv7d.year.value"], "error_msgs": ["Year does not match across documents"], "conditionType": "AND", "groups": ["mv-1"]}]}, {"validation_rules": [{"id": "titlemso.year.value", "validation_type": "REGEX_LIST", "regexes": ["^(19[0-9]{2}|20[0-2][0-9]|2030)$"], "error_msgs": ["Year is required and should be between 1900 and 2030"], "conditionType": "AND", "groups": ["<PERSON><PERSON><PERSON>"]}, {"id": "titlemso.year.consistency", "validation_type": "EXPRESSION_TYPE_LIST", "expressions": ["bill_of_sale.year.value == mv-1.year.value and mv-1.year.value == titlemso.year.value and titlemso.year.value == red_reassignment.year.value or bill_of_sale.year.value == mv-1.year.value and mv-1.year.value == titlemso.year.value and titlemso.year.value == mv7d.year.value"], "error_msgs": ["Year does not match across documents"], "conditionType": "AND", "groups": ["<PERSON><PERSON><PERSON>"]}]}, {"validation_rules": [{"id": "red_reassignment.year.value", "validation_type": "REGEX_LIST", "regexes": ["^(19[0-9]{2}|20[0-2][0-9]|2030)$"], "error_msgs": ["Year is required and should be between 1900 and 2030"], "conditionType": "AND", "groups": ["red_reassignment"]}, {"id": "red_reassignment.year.consistency", "validation_type": "EXPRESSION_TYPE_LIST", "expressions": ["bill_of_sale.year.value == mv-1.year.value and mv-1.year.value == titlemso.year.value and titlemso.year.value == red_reassignment.year.value or bill_of_sale.year.value == mv-1.year.value and mv-1.year.value == titlemso.year.value and titlemso.year.value == mv7d.year.value"], "error_msgs": ["Year does not match across documents"], "conditionType": "AND", "groups": ["red_reassignment"]}]}, {"validation_rules": [{"id": "mv7d.year.value", "validation_type": "REGEX_LIST", "regexes": ["^(19[0-9]{2}|20[0-2][0-9]|2030)$"], "error_msgs": ["Year is required and should be between 1900 and 2030"], "conditionType": "AND", "groups": ["mv7d"]}, {"id": "mv7d.year.consistency", "validation_type": "EXPRESSION_TYPE_LIST", "expressions": ["bill_of_sale.year.value == mv-1.year.value and mv-1.year.value == titlemso.year.value and titlemso.year.value == red_reassignment.year.value or bill_of_sale.year.value == mv-1.year.value and mv-1.year.value == titlemso.year.value and titlemso.year.value == mv7d.year.value"], "error_msgs": ["Year does not match across documents"], "conditionType": "AND", "groups": ["mv7d"]}]}, {"validation_rules": [{"id": "bill_of_sale.make.value", "validation_type": "REGEX_LIST", "regexes": ["^[A-Za-z\\s-]{1,30}$"], "error_msgs": ["Make is required and should contain only letters, spaces, and hyphens (1-30 characters)"], "conditionType": "AND", "groups": ["bill_of_sale"]}, {"id": "bill_of_sale.make.consistency", "validation_type": "EXPRESSION_TYPE_LIST", "expressions": ["bill_of_sale.make.value == mv-1.make.value and mv-1.make.value == titlemso.make.value and titlemso.make.value == red_reassignment.make.value or bill_of_sale.make.value == mv-1.make.value and mv-1.make.value == titlemso.make.value and titlemso.make.value == mv7d.make.value"], "error_msgs": ["Make does not match across documents"], "conditionType": "AND", "groups": ["bill_of_sale"]}]}, {"validation_rules": [{"id": "mv-1.make.value", "validation_type": "REGEX_LIST", "regexes": ["^[A-Za-z\\s-]{1,30}$"], "error_msgs": ["Make is required and should contain only letters, spaces, and hyphens (1-30 characters)"], "conditionType": "AND", "groups": ["mv-1"]}, {"id": "mv-1.make.consistency", "validation_type": "EXPRESSION_TYPE_LIST", "expressions": ["bill_of_sale.make.value == mv-1.make.value and mv-1.make.value == titlemso.make.value and titlemso.make.value == red_reassignment.make.value or bill_of_sale.make.value == mv-1.make.value and mv-1.make.value == titlemso.make.value and titlemso.make.value == mv7d.make.value"], "error_msgs": ["Make does not match across documents"], "conditionType": "AND", "groups": ["mv-1"]}]}, {"validation_rules": [{"id": "titlemso.make.value", "validation_type": "REGEX_LIST", "regexes": ["^[A-Za-z\\s-]{1,30}$"], "error_msgs": ["Make is required and should contain only letters, spaces, and hyphens (1-30 characters)"], "conditionType": "AND", "groups": ["<PERSON><PERSON><PERSON>"]}, {"id": "titlemso.make.consistency", "validation_type": "EXPRESSION_TYPE_LIST", "expressions": ["bill_of_sale.make.value == mv-1.make.value and mv-1.make.value == titlemso.make.value and titlemso.make.value == red_reassignment.make.value or bill_of_sale.make.value == mv-1.make.value and mv-1.make.value == titlemso.make.value and titlemso.make.value == mv7d.make.value"], "error_msgs": ["Make does not match across documents"], "conditionType": "AND", "groups": ["<PERSON><PERSON><PERSON>"]}]}, {"validation_rules": [{"id": "red_reassignment.make.value", "validation_type": "REGEX_LIST", "regexes": ["^[A-Za-z\\s-]{1,30}$"], "error_msgs": ["Make is required and should contain only letters, spaces, and hyphens (1-30 characters)"], "conditionType": "AND", "groups": ["red_reassignment"]}, {"id": "red_reassignment.make.consistency", "validation_type": "EXPRESSION_TYPE_LIST", "expressions": ["bill_of_sale.make.value == mv-1.make.value and mv-1.make.value == titlemso.make.value and titlemso.make.value == red_reassignment.make.value or bill_of_sale.make.value == mv-1.make.value and mv-1.make.value == titlemso.make.value and titlemso.make.value == mv7d.make.value"], "error_msgs": ["Make does not match across documents"], "conditionType": "AND", "groups": ["red_reassignment"]}]}, {"validation_rules": [{"id": "mv7d.make.value", "validation_type": "REGEX_LIST", "regexes": ["^[A-Za-z\\s-]{1,30}$"], "error_msgs": ["Make is required and should contain only letters, spaces, and hyphens (1-30 characters)"], "conditionType": "AND", "groups": ["mv7d"]}, {"id": "mv7d.make.consistency", "validation_type": "EXPRESSION_TYPE_LIST", "expressions": ["bill_of_sale.make.value == mv-1.make.value and mv-1.make.value == titlemso.make.value and titlemso.make.value == red_reassignment.make.value or bill_of_sale.make.value == mv-1.make.value and mv-1.make.value == titlemso.make.value and titlemso.make.value == mv7d.make.value"], "error_msgs": ["Make does not match across documents"], "conditionType": "AND", "groups": ["mv7d"]}]}, {"validation_rules": [{"id": "bill_of_sale.model.value", "validation_type": "REGEX_LIST", "regexes": ["^[A-Za-z0-9\\s-]{1,30}$"], "error_msgs": ["Model is required and should contain only letters, numbers, spaces, and hyphens (1-30 characters)"], "conditionType": "AND", "groups": ["bill_of_sale"]}, {"id": "bill_of_sale.model.consistency", "validation_type": "EXPRESSION_TYPE_LIST", "expressions": ["bill_of_sale.model.value == mv-1.model.value and mv-1.model.value == titlemso.model.value and titlemso.model.value == red_reassignment.model.value or bill_of_sale.model.value == mv-1.model.value and mv-1.model.value == titlemso.model.value and titlemso.model.value == mv7d.model.value"], "error_msgs": ["Model does not match across documents"], "conditionType": "AND", "groups": ["bill_of_sale"]}]}, {"validation_rules": [{"id": "mv-1.model.value", "validation_type": "REGEX_LIST", "regexes": ["^[A-Za-z0-9\\s-]{1,30}$"], "error_msgs": ["Model is required and should contain only letters, numbers, spaces, and hyphens (1-30 characters)"], "conditionType": "AND", "groups": ["mv-1"]}, {"id": "mv-1.model.consistency", "validation_type": "EXPRESSION_TYPE_LIST", "expressions": ["bill_of_sale.model.value == mv-1.model.value and mv-1.model.value == titlemso.model.value and titlemso.model.value == red_reassignment.model.value or bill_of_sale.model.value == mv-1.model.value and mv-1.model.value == titlemso.model.value and titlemso.model.value == mv7d.model.value"], "error_msgs": ["Model does not match across documents"], "conditionType": "AND", "groups": ["mv-1"]}]}, {"validation_rules": [{"id": "bill_of_sale.odometer_reading.value", "validation_type": "REGEX_LIST", "regexes": ["^(0|[1-9][0-9]{0,5})$"], "error_msgs": ["Odometer reading should be a positive number between 0 and 999,999"], "conditionType": "AND", "groups": ["bill_of_sale"]}, {"id": "bill_of_sale.odometer_reading.consistency", "validation_type": "EXPRESSION_TYPE_LIST", "expressions": ["abs(safe_subtract(bill_of_sale.odometer_reading.value, mv-1.odometer_reading.value)) <= 10 and abs(safe_subtract(mv-1.odometer_reading.value, red_reassignment.odometer_reading.value)) <= 10 and abs(safe_subtract(red_reassignment.odometer_reading.value, titlemso.odometer_reading.value)) <= 10", "titlemso.odometer_reading.value <= bill_of_sale.odometer_reading.value"], "error_msgs": ["Odometer reading does not match across documents", "Title odometer may be less than or equal to BOS odometer reading - this is acceptable"], "conditionType": "AND", "groups": ["bill_of_sale"]}]}, {"validation_rules": [{"id": "mv-1.odometer_reading.value", "validation_type": "REGEX_LIST", "regexes": ["^(0|[1-9][0-9]{0,5})$"], "error_msgs": ["Odometer reading should be a positive number between 0 and 999,999"], "conditionType": "AND", "groups": ["mv-1"]}, {"id": "mv-1.odometer_reading.consistency", "validation_type": "EXPRESSION_TYPE_LIST", "expressions": ["abs(safe_subtract(bill_of_sale.odometer_reading.value, mv-1.odometer_reading.value)) <= 10 and abs(safe_subtract(mv-1.odometer_reading.value, red_reassignment.odometer_reading.value)) <= 10 and abs(safe_subtract(red_reassignment.odometer_reading.value, titlemso.odometer_reading.value)) <= 10", "titlemso.odometer_reading.value <= bill_of_sale.odometer_reading.value"], "error_msgs": ["Odometer reading does not match across documents", "Title odometer may be less than or equal to BOS odometer reading - this is acceptable"], "conditionType": "AND", "groups": ["mv-1"]}]}, {"validation_rules": [{"id": "bill_of_sale.buyer_name.value", "validation_type": "REGEX_LIST", "regexes": ["^.{1,}$", "^[A-Za-z\\s'-]{2,50}$"], "error_msgs": ["Buyer name is required", "Buyer name should contain only letters, spaces, hyphens and apostrophes (2-50 characters)"], "conditionType": "AND", "groups": ["bill_of_sale"]}, {"id": "bill_of_sale.buyer_name.consistency", "validation_type": "EXPRESSION_TYPE_LIST", "expressions": ["bill_of_sale.buyer_name.value == driver_license.full_name.value and driver_license.full_name.value == mv-1.buyer_full_name.value and mv-1.buyer_full_name.value == red_reassignment.buyer_name.value and red_reassignment.buyer_name.value == titlemso.buyer_name.value or bill_of_sale.buyer_name.value == driver_license.full_name.value and driver_license.full_name.value == mv-1.buyer_full_name.value and mv-1.buyer_full_name.value == mv7d.buyer_name.value and .buyer_name.value == titlemso.co_buyer_name.value or bill_of_sale.buyer_name.value == driver_license.full_name.value and driver_license.full_name.value == mv-1.buyer_full_name.value and mv-1.buyer_full_name.value"], "error_msgs": ["Buyer name does not match across documents"], "conditionType": "AND", "groups": ["bill_of_sale"]}]}, {"validation_rules": [{"id": "driver_license.full_name.value", "validation_type": "REGEX_LIST", "regexes": ["^.{1,}$", "^[A-Za-z\\s'-]{2,50}$"], "error_msgs": ["Full name is required", "Full name should contain only letters, spaces, hyphens and apostrophes (2-50 characters)"], "conditionType": "AND", "groups": ["driver_license"]}, {"id": "driver_license.full_name.consistency", "validation_type": "EXPRESSION_TYPE_LIST", "expressions": ["bill_of_sale.buyer_name.value == mv-1.buyer_full_name.value"], "error_msgs": ["Full name does not match across documents"], "conditionType": "AND", "groups": ["driver_license"]}]}, {"validation_rules": [{"id": "driver_license.date_of_birth.value", "validation_type": "REGEX_LIST", "regexes": ["^(0[1-9]|1[0-2])\\/(0[1-9]|[12]\\d|3[01])\\/((19[0-9]{2})|200[0-9])$", "^(0[1-9]|1[0-2])\\/(0[1-9]|[12]\\d|3[01])\\/((19[0-9]{2})|200[0-8])$"], "error_msgs": ["Date of birth is required and should be in MM/DD/YYYY format with a valid year between 1900 and 2009", "Date of birth should be in MM/DD/YYYY format and indicate an age between 16 and 120 years"], "conditionType": "AND", "groups": ["driver_license"]}]}, {"validation_rules": [{"id": "driver_license.address.value", "validation_type": "REGEX_LIST", "regexes": ["^.{1,}$", "^[0-9]+\\s+[A-Za-z0-9\\s,.-]{1,100}$"], "error_msgs": ["address is required", "address should start with a number followed by street name (1-100 characters)"], "conditionType": "AND", "groups": ["driver_license"]}]}, {"validation_rules": [{"id": "driver_license.city.value", "validation_type": "REGEX_LIST", "regexes": ["^.{1,}$", "^[A-Za-z\\s'-]{2,50}$"], "error_msgs": ["City is required", "City should contain only letters, spaces, hyphens and apostrophes (2-50 characters)"], "conditionType": "AND", "groups": ["driver_license"]}]}, {"validation_rules": [{"id": "driver_license.state.value", "validation_type": "REGEX_LIST", "regexes": ["^.{1,}$", "^[A-Z]{2}$"], "error_msgs": ["State is required", "State should be a 2-letter abbreviation (e.g., GA, FL)"], "conditionType": "AND", "groups": ["driver_license"]}]}, {"validation_rules": [{"id": "driver_license.zip.value", "validation_type": "REGEX_LIST", "regexes": ["^.{1,}$", "^[0-9]{5}(-[0-9]{4})?$"], "error_msgs": ["ZIP code is required", "ZIP code should be 5 digits or 5+4 format (e.g., 12345 or 12345-6789)"], "conditionType": "AND", "groups": ["driver_license"]}]}, {"validation_rules": [{"id": "driver_license.driver_s_license_number.value", "validation_type": "REGEX_LIST", "regexes": ["^.{1,}$", "^[A-Za-z0-9-]{4,25}$"], "error_msgs": ["Driver's license number is required", "Driver's license number should be 4-25 alphanumeric characters (may include hyphens)"], "conditionType": "AND", "groups": ["driver_license"]}, {"id": "driver_license.driver_s_license_number.consistency", "validation_type": "EXPRESSION_TYPE_LIST", "expressions": ["driver_license.driver_s_license_number.value == mv-1.customer_id.value and (driver_license.driver_s_license_number.value == red_reassignment.customer_id.value or driver_license.driver_s_license_number.value == mv7d.customer_id.value)"], "error_msgs": ["Driver's license number does not match across documents"], "conditionType": "AND", "groups": ["driver_license"]}]}, {"validation_rules": [{"id": "driver_license.expiration_date.value", "validation_type": "REGEX_LIST", "regexes": ["^.+$", "^(0[1-9]|1[0-2])\\/(0[1-9]|[12]\\d|3[01])\\/(202[5-9]|20[3-9]\\d|21\\d{2})$"], "error_msgs": ["Driver's license expiration date is required", "Expiration date should be in MM/DD/YYYY format and must not be expired"], "conditionType": "AND", "groups": ["driver_license"]}, {"id": "driver_license.expiration_date.consistency", "validation_type": "EXPRESSION_TYPE_LIST", "expressions": ["date_is_future(driver_license.expiration_date.value)"], "error_msgs": ["Expiration date must be in the future"], "conditionType": "AND", "groups": ["driver_license"]}]}, {"validation_rules": [{"id": "mv-1.buyer_full_name.value", "validation_type": "REGEX_LIST", "regexes": ["^.{1,}$", "^[A-Za-z\\s'-]{2,50}$"], "error_msgs": ["Buyer full name is required", "Buyer full name should contain only letters, spaces, hyphens and apostrophes (2-50 characters)"], "conditionType": "AND", "groups": ["mv-1"]}, {"id": "mv-1.buyer_full_name.consistency", "validation_type": "EXPRESSION_TYPE_LIST", "expressions": ["bill_of_sale.buyer_name.value == mv-1.buyer_full_name.value and mv-1.buyer_full_name.value == driver_license.full_name.value"], "error_msgs": ["Buyer full name does not match across documents"], "conditionType": "AND", "groups": ["mv-1"]}]}, {"validation_rules": [{"id": "mv-1.co_buyer_name.value", "validation_type": "REGEX_LIST", "regexes": ["^[A-Za-z\\s'-]{2,50}$"], "error_msgs": ["Co-buyer name should contain only letters, spaces, hyphens and apostrophes (2-50 characters)"], "conditionType": "AND", "groups": ["mv-1"]}, {"id": "mv-1.co_buyer_name.consistency", "validation_type": "EXPRESSION_TYPE_LIST", "expressions": ["mv-1.co_buyer_name.value == bill_of_sale.co_buyer_name.value"], "error_msgs": ["Co-buyer name does not match across documents"], "conditionType": "AND", "groups": ["mv-1"]}]}, {"validation_rules": [{"id": "mv-1.buyer_address.value", "validation_type": "REGEX_LIST", "regexes": ["^.{1,}$", "^[0-9]+\\s+[A-Za-z0-9\\s,.-]{1,100}$"], "error_msgs": ["Buyer address is required", "Buyer address should start with a number followed by street name (1-100 characters)"], "conditionType": "AND", "groups": ["mv-1"]}, {"id": "mv-1.buyer_address.consistency", "validation_type": "EXPRESSION_TYPE_LIST", "expressions": ["mv-1.buyer_address.value == driver_license.address.value and mv-1.buyer_address.value == bill_of_sale.buyer_address.value"], "error_msgs": ["Buyer address does not match across documents"], "conditionType": "AND", "groups": ["mv-1"]}]}, {"validation_rules": [{"id": "mv-1.county_of_residence.value", "validation_type": "REGEX_LIST", "regexes": ["^.{1,}$", "^[A-Za-z\\s'-]{2,50}$"], "error_msgs": ["County of residence is required", "County should contain only letters, spaces, hyphens and apostrophes (2-50 characters)"], "conditionType": "AND", "groups": ["mv-1"]}]}, {"validation_rules": [{"id": "mv-1.body_style.value", "validation_type": "REGEX_LIST", "regexes": ["^[A-Za-z0-9\\s-]{1,20}$"], "error_msgs": ["Body style should contain only letters, numbers, spaces, and hyphens (1-20 characters)"], "conditionType": "AND", "groups": ["mv-1"]}]}, {"validation_rules": [{"id": "mv-1.lien_holder_address.value", "validation_type": "REGEX_LIST", "regexes": ["^.{1,}$"], "error_msgs": ["Lien holder address is required"], "conditionType": "AND", "groups": ["mv-1"]}]}, {"validation_rules": [{"id": "mv-1.lien_holder_name.value", "validation_type": "REGEX_LIST", "regexes": ["^[A-Za-z0-9\\s&.,'-]{2,100}$"], "error_msgs": ["Lien holder name should be 2-100 characters with valid business name characters"], "conditionType": "AND", "groups": ["mv-1"]}, {"id": "mv-1.lien_holder_name.consistency", "validation_type": "EXPRESSION_TYPE_LIST", "expressions": ["mv-1.lien_holder_name.value == bill_of_sale.lien_holder_name.value and mv-1.lien_holder_name.value == titlemso.lien_holder_name.value"], "error_msgs": ["Lien holder name does not match across documents"], "conditionType": "AND", "groups": ["mv-1"]}]}, {"validation_rules": [{"id": "mv-1.dealer_name.value", "validation_type": "REGEX_LIST", "regexes": ["^.{1,}$", "^[A-Za-z0-9\\s&.,'-]{2,100}$"], "error_msgs": ["Dealer name is required", "Dealer name should be 2-100 characters with valid business name characters"], "conditionType": "AND", "groups": ["mv-1"]}, {"id": "mv-1.dealer_name.consistency", "validation_type": "EXPRESSION_TYPE_LIST", "expressions": ["mv-1.dealer_name.value == bill_of_sale.buyer_name.value"], "error_msgs": ["Dealer name does not match across documents"], "conditionType": "AND", "groups": ["mv-1"]}]}, {"validation_rules": [{"id": "mv-1.dealer_number.value", "validation_type": "REGEX_LIST", "regexes": ["^.{1,}$", "^[A-Za-z0-9]{3,20}$"], "error_msgs": ["Dealer number is required", "Dealer number should be 3-20 alphanumeric characters"], "conditionType": "AND", "groups": ["mv-1"]}]}, {"validation_rules": [{"id": "mv-1.customer_id.value", "validation_type": "REGEX_LIST", "regexes": ["^.{1,}$", "^[A-Za-z0-9-]{4,25}$"], "error_msgs": ["Customer ID (Driver's License Number) is required", "Customer ID should be 4-25 alphanumeric characters (may include hyphens)"], "conditionType": "AND", "groups": ["mv-1"]}, {"id": "mv-1.customer_id.consistency", "validation_type": "EXPRESSION_TYPE_LIST", "expressions": ["mv-1.customer_id.value == driver_license.driver_s_license_number.value"], "error_msgs": ["Customer ID does not match Driver's License number"], "conditionType": "AND", "groups": ["mv-1"]}]}, {"validation_rules": [{"id": "mv-1.tavt_tax_amount.value", "validation_type": "REGEX_LIST", "regexes": ["^(0(\\.\\d{1,2})?|[1-9]\\d{0,4}(\\.\\d{1,2})?|99999(\\.99?)?)$", "^.+$"], "error_msgs": ["TAVT tax amount is required and should be between $0 and $99,999.99", "TAVT tax amount is required"], "conditionType": "AND", "groups": ["mv-1"]}, {"id": "mv-1.tavt_tax_amount.consistency", "validation_type": "EXPRESSION_TYPE_LIST", "expressions": ["abs(safe_subtract(bill_of_sale.tavt_tax_amount.value, mv-1.tavt_tax_amount.value)) <= 500 and abs(safe_subtract(mv-1.tavt_tax_amount.value, dealer_dmv.tavt_tax_amount.value)) <= 500", "bill_of_sale.field.value == mv-1.field.value and mv-1.field.value == dealer_dmv.field.value"], "error_msgs": ["TAVT tax amount differs by more than $500 across documents - requires manual review", "TAVT calculation does not match expected amount based on sale price and county tax rate"], "conditionType": "AND", "groups": ["mv-1"]}]}, {"validation_rules": [{"id": "mv-1.sales_price.value", "validation_type": "REGEX_LIST", "regexes": ["^(0\\.(0[1-9]|[1-9][0-9]?)|[1-9][0-9]{0,5}(\\.[0-9]{1,2})?|999999(\\.99?)?)$"], "error_msgs": ["Sales price should be a positive amount between $0.01 and $999,999.99"], "conditionType": "AND", "groups": ["mv-1"]}, {"id": "mv-1.sales_price.consistency", "validation_type": "EXPRESSION_TYPE_LIST", "expressions": ["abs(safe_subtract(mv-1.sales_price.value, bill_of_sale.sale_price.value)) <= 100"], "error_msgs": ["Sales price does not match between MV-1 and BOS (difference > $100)"], "conditionType": "AND", "groups": ["mv-1"]}]}, {"validation_rules": [{"id": "red_reassignment.customer_id.value", "validation_type": "REGEX_LIST", "regexes": ["^.{1,}$", "^[A-Za-z0-9]{6,20}$"], "error_msgs": ["Customer ID (Driver's License Number) is required", "Customer ID should be 6-20 alphanumeric characters"], "conditionType": "AND", "groups": ["red_reassignment"]}, {"id": "red_reassignment.customer_id.consistency", "validation_type": "EXPRESSION_TYPE_LIST", "expressions": ["driver_license.driver_s_license_number.value == mv-1.customer_id.value and (driver_license.driver_s_license_number.value == red_reassignment.customer_id.value or driver_license.driver_s_license_number.value == mv7d.customer_id.value)"], "error_msgs": ["Customer ID does not match across documents"], "conditionType": "AND", "groups": ["red_reassignment"]}]}, {"validation_rules": [{"id": "red_reassignment.odometer_type.value", "validation_type": "REGEX_LIST", "regexes": ["^.{1,}$", "^(Actual|ACTUAL|Exceeds|EXCEEDS|Not Actual|NOT ACTUAL)$"], "error_msgs": ["Odometer type is required", "Odometer type should be 'Actual', 'Exceeds', or 'Not Actual'"], "conditionType": "AND", "groups": ["red_reassignment"]}]}, {"validation_rules": [{"id": "red_reassignment.date_of_reassignment.value", "validation_type": "REGEX_LIST", "regexes": ["^(0[1-9]|1[0-2])\\/(0[1-9]|[12]\\d|3[01])\\/(200\\d|201\\d|202[0-5])$", "^.+$"], "error_msgs": ["Date of reassignment should be in MM/DD/YYYY format and between 2000 and 2025", "Date of reassignment is required"], "conditionType": "AND", "groups": ["red_reassignment"]}, {"id": "red_reassignment.date_sequence", "validation_type": "EXPRESSION_TYPE_LIST", "expressions": ["true"], "error_msgs": ["Reassignment date must be before transfer date"], "conditionType": "AND", "groups": ["red_reassignment"]}]}, {"validation_rules": [{"id": "titlemso.selling_dealer_name.value", "validation_type": "REGEX_LIST", "regexes": ["^.{1,}$", "^[A-Za-z0-9\\s&.,'-]{2,100}$"], "error_msgs": ["Selling dealer name is required", "Selling dealer name should be 2-100 characters with valid business name characters"], "conditionType": "AND", "groups": ["<PERSON><PERSON><PERSON>"]}, {"id": "titlemso.selling_dealer_name.consistency", "validation_type": "EXPRESSION_TYPE_LIST", "expressions": ["titlemso.selling_dealer_name.value == bill_of_sale.buyer_name.value or titlemso.selling_dealer_name.value == red_reassignment.buyer_name.value"], "error_msgs": ["Selling dealer name does not match across documents"], "conditionType": "AND", "groups": ["<PERSON><PERSON><PERSON>"]}]}, {"validation_rules": [{"id": "titlemso.lien_satisfied.value", "validation_type": "EXPRESSION_TYPE_LIST", "expressions": ["value == True or value == False or value == \"true\" or value == \"false\" or value == 1 or value == 0"], "error_msgs": ["Lien satisfied should be true or false"], "conditionType": "AND", "groups": ["<PERSON><PERSON><PERSON>"]}]}, {"validation_rules": [{"id": "titlemso.date_of_transfer.value", "validation_type": "REGEX_LIST", "regexes": ["^.+$", "^(0[1-9]|1[0-2])\\/(0[1-9]|[12]\\d|3[01])\\/(200\\d|201\\d|202[0-5])$"], "error_msgs": ["Date of transfer is required", "Date of transfer should be in MM/DD/YYYY format and between 01/01/2000 and current date"], "conditionType": "AND", "groups": ["<PERSON><PERSON><PERSON>"]}]}, {"validation_rules": [{"id": "titlemso.title_number.value", "validation_type": "REGEX_LIST", "regexes": ["^.{1,}$", "^[A-Za-z0-9]{6,20}$"], "error_msgs": ["Title number is required", "Title number should be 6-20 alphanumeric characters"], "conditionType": "AND", "groups": ["<PERSON><PERSON><PERSON>"]}]}, {"validation_rules": [{"id": "titlemso.lien_release_section.value", "validation_type": "REGEX_LIST", "regexes": ["^[A-Za-z0-9\\s&.,'-]{2,200}$"], "error_msgs": ["Lien release section should be 2-200 characters with valid text characters"], "conditionType": "AND", "groups": ["<PERSON><PERSON><PERSON>"]}]}, {"validation_rules": [{"id": "bill_of_sale.sale_price.value", "validation_type": "REGEX_LIST", "regexes": ["^(0\\.(0[1-9]|[1-9][0-9]?)|[1-9][0-9]{0,5}(\\.[0-9]{1,2})?|999999(\\.99?)?)$"], "error_msgs": ["Sale price should be a positive amount between $0.01 and $999,999.99"], "conditionType": "AND", "groups": ["bill_of_sale"]}, {"id": "bill_of_sale.sale_price.consistency", "validation_type": "EXPRESSION_TYPE_LIST", "expressions": ["abs(safe_subtract(bill_of_sale.sale_price.value, mv-1.sales_price.value)) <= 100"], "error_msgs": ["Sale price does not match between BOS and MV-1 (difference > $100)"], "conditionType": "AND", "groups": ["bill_of_sale"]}]}, {"validation_rules": [{"id": "bill_of_sale.trade_in_value.value", "validation_type": "REGEX_LIST", "regexes": ["^(0(\\.\\d{1,2})?|[1-9]\\d{0,5}(\\.\\d{1,2})?|999999(\\.99?)?)$"], "error_msgs": ["Trade-in value should be a positive amount up to $999,999.99"], "conditionType": "AND", "groups": ["bill_of_sale"]}, {"id": "bill_of_sale.trade_in_value.consistency", "validation_type": "EXPRESSION_TYPE_LIST", "expressions": ["True"], "error_msgs": ["Trade-in value does not match between Bill of Sale and Red Reassignment"], "conditionType": "AND", "groups": ["bill_of_sale"]}]}, {"validation_rules": [{"id": "bill_of_sale.tavt_tax_amount.value", "validation_type": "REGEX_LIST", "regexes": ["^(0(\\.\\d{1,2})?|[1-9]\\d{0,4}(\\.\\d{1,2})?|99999(\\.99?)?)$"], "error_msgs": ["TAVT tax amount is required and should be between $0 and $99,999.99"], "conditionType": "AND", "groups": ["bill_of_sale"]}]}, {"validation_rules": [{"id": "bill_of_sale.total_amount_due.value", "validation_type": "REGEX_LIST", "regexes": ["^(0(\\.\\d{1,2})?|[1-9]\\d{0,5}(\\.\\d{1,2})?|999999(\\.99?)?)$"], "error_msgs": ["Total amount due should be a positive amount up to $999,999.99"], "conditionType": "AND", "groups": ["bill_of_sale"]}, {"id": "bill_of_sale.total_amount_due.consistency", "validation_type": "EXPRESSION_TYPE_LIST", "expressions": ["abs(safe_subtract(bill_of_sale.total_amount_due.value, mv-1.total_amount_due.value)) <= 100 and abs(safe_subtract(mv-1.total_amount_due.value, red_reassignment.total_amount_due.value)) <= 100"], "error_msgs": ["Total amount due does not match across funding and documents"], "conditionType": "AND", "groups": ["bill_of_sale"]}]}, {"validation_rules": [{"id": "bill_of_sale.dealer_fees.value", "validation_type": "REGEX_LIST", "regexes": ["^(0(\\.\\d{1,2})?|[1-9]\\d{0,4}(\\.\\d{1,2})?|99999(\\.99?)?)$"], "error_msgs": ["Dealer fees should be between $0 and $99,999.99"], "conditionType": "AND", "groups": ["bill_of_sale"]}]}, {"validation_rules": [{"id": "bill_of_sale.lien_holder_name.value", "validation_type": "REGEX_LIST", "regexes": ["^[A-Za-z0-9\\s&.,'-]{2,100}$"], "error_msgs": ["Lien holder name should be 2-100 characters with valid business name characters"], "conditionType": "AND", "groups": ["bill_of_sale"]}, {"id": "bill_of_sale.lien_holder_name.consistency", "validation_type": "EXPRESSION_TYPE_LIST", "expressions": ["bill_of_sale.lien_holder_name.value == mv-1.lien_holder_name.value and mv-1.lien_holder_name.value == titlemso.lien_holder_name.value"], "error_msgs": ["Lien holder name does not match across documents"], "conditionType": "AND", "groups": ["bill_of_sale"]}]}, {"validation_rules": [{"id": "bill_of_sale.co_buyer_name.value", "validation_type": "REGEX_LIST", "regexes": ["^[A-Za-z\\s'-]{2,50}$"], "error_msgs": ["Co-buyer name should contain only letters, spaces, hyphens and apostrophes"], "conditionType": "AND", "groups": ["bill_of_sale"]}, {"id": "bill_of_sale.co_buyer_name.consistency", "validation_type": "EXPRESSION_TYPE_LIST", "expressions": ["bill_of_sale.co_buyer_name.value == mv-1.co_buyer_name.value"], "error_msgs": ["Co-buyer name does not match across documents"], "conditionType": "AND", "groups": ["bill_of_sale"]}]}, {"validation_rules": [{"id": "bill_of_sale.buyer_address.value", "validation_type": "REGEX_LIST", "regexes": ["^.{1,}$"], "error_msgs": ["Buyer address is required"], "conditionType": "AND", "groups": ["bill_of_sale"]}, {"id": "bill_of_sale.buyer_address.consistency", "validation_type": "EXPRESSION_TYPE_LIST", "expressions": ["bill_of_sale.buyer_address.value == mv-1.buyer_address.value and mv-1.buyer_address.value == driver_license.address.value and (red_reassignment.buyer_address.value == bill_of_sale.buyer_address.value or mv7d.buyer_address.value == bill_of_sale.buyer_address.value)"], "error_msgs": ["Buyer address does not match across documents (BOS, MV-1, Driver's License, MV-7D)"], "conditionType": "AND", "groups": ["bill_of_sale"]}]}, {"validation_rules": [{"id": "bill_of_sale.deal_status.value", "validation_type": "REGEX_LIST", "regexes": ["^.{1,}$", "^(funded|accounting|finalized)$"], "error_msgs": ["Deal status is required", "Deal must be fully finalized (funded, in accounting, or finalized) before processing"], "conditionType": "AND", "groups": ["bill_of_sale"]}]}, {"validation_rules": [{"id": "bill_of_sale.title_received.value", "validation_type": "EXPRESSION_TYPE_LIST", "expressions": ["value == True or value == False or value == \"true\" or value == \"false\" or value == 1 or value == 0"], "error_msgs": ["Title or MSO must be physically or digitally available before processing"], "conditionType": "AND", "groups": ["bill_of_sale"]}]}, {"validation_rules": [{"id": "bill_of_sale.vehicle_type.value", "validation_type": "REGEX_LIST", "regexes": ["^.{1,}$", "^(new|used)$"], "error_msgs": ["Vehicle type is required", "Vehicle type must be 'new' or 'used'"], "conditionType": "AND", "groups": ["bill_of_sale"]}]}, {"validation_rules": [{"id": "mv7d.customer_id.value", "validation_type": "REGEX_LIST", "regexes": ["^.{1,}$", "^[A-Za-z0-9-]{4,25}$"], "error_msgs": ["Customer ID (Driver's License Number) is required", "Customer ID should be 4-25 alphanumeric characters (may include hyphens)"], "conditionType": "AND", "groups": ["mv7d"]}, {"id": "mv7d.customer_id.consistency", "validation_type": "EXPRESSION_TYPE_LIST", "expressions": ["red_reassignment.customer_id.value == driver_license.driver_s_license_number.value or  mv7d.customer_id.value == driver_license.driver_s_license_number.value"], "error_msgs": ["Customer ID does not match across documents"], "conditionType": "AND", "groups": ["mv7d"]}]}, {"validation_rules": [{"id": "mv7d.signatures.value", "validation_type": "EXPRESSION_TYPE_LIST", "expressions": ["seller_signature.value != None and seller_signature.value != \"\" and buyer_signature.value != None and buyer_signature.value != \"\""], "error_msgs": ["Both seller and buyer signatures are required for MV-7D"], "conditionType": "AND", "groups": ["mv7d"]}]}, {"validation_rules": [{"id": "dealer_dmv.dealer_fees.value", "validation_type": "REGEX_LIST", "regexes": ["^(0(\\.\\d{1,2})?|[1-9]\\d{0,4}(\\.\\d{1,2})?|99999(\\.99?)?)$", "^.+$"], "error_msgs": ["Dealer fees should be a positive amount between $0 and $99,999.99", "Dealer fees is required"], "conditionType": "AND", "groups": ["dealer_dmv"]}]}, {"validation_rules": [{"id": "red_reassignment.seller_signature.value", "validation_type": "EXPRESSION_TYPE_LIST", "expressions": ["value != None and value != \"\" and value != False"], "error_msgs": ["Seller signature is required"], "conditionType": "AND", "groups": ["red_reassignment"]}, {"id": "red_reassignment.buyer_signature.value", "validation_type": "EXPRESSION_TYPE_LIST", "expressions": ["value != None and value != \"\" and value != False"], "error_msgs": ["Buyer signature is required"], "conditionType": "AND", "groups": ["red_reassignment"]}]}]}